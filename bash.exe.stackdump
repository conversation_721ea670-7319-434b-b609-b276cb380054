Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE129F0000 ntdll.dll
7FFE10A20000 KERNEL32.DLL
7FFE0FCF0000 KERNELBASE.dll
7FFE119A0000 USER32.dll
7FFE0FB80000 win32u.dll
7FFE11970000 GDI32.dll
7FFE103E0000 gdi32full.dll
7FFE0FAE0000 msvcp_win.dll
7FFE10240000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE127F0000 advapi32.dll
7FFE12620000 msvcrt.dll
7FFE11E30000 sechost.dll
7FFE10190000 bcrypt.dll
7FFE12410000 RPCRT4.dll
7FFE0F270000 CRYPTBASE.DLL
7FFE101C0000 bcryptPrimitives.dll
7FFE10AF0000 IMM32.DLL
