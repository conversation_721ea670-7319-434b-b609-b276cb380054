/*import 'dart:async';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

class LocationProvider with ChangeNotifier {
  Position? _currentPosition;
  Stream<Position>? _positionStream;
  late final StreamSubscription<Position> _subscription;

  Position? get currentPosition => _currentPosition;

  LocationProvider() {
    _initLocationUpdates();
  }

  Future<void> _initLocationUpdates() async {
    await _checkPermission();

    _positionStream = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 5, // meters
      ),
    );

    _subscription = _positionStream!.listen((Position position) async {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      LocationPermission permission = await Geolocator.checkPermission();

      if (!serviceEnabled || permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
        _currentPosition = null;
      } else {
        _currentPosition = position;
      }
      notifyListeners();
    });
  }

  Future<void> _checkPermission() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
       await Geolocator.requestPermission();
        serviceEnabled = await Geolocator.isLocationServiceEnabled();
       if (!serviceEnabled) {
      }
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied.');
    }

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission != LocationPermission.whileInUse &&
          permission != LocationPermission.always) {
        throw Exception('Location permission not granted.');
      }
    }
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}*/
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:location/location.dart';

class LocationProvider with ChangeNotifier {
  Position? _currentLocation;
  bool _serviceEnabled = false;
  LocationPermission _permissionGranted = LocationPermission.denied;

  Position? get currentPosition => _currentLocation;

  LocationProvider() {
    _initLocationService();
  }

  Future<void> _initLocationService() async {
    _serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!_serviceEnabled) {
      _currentLocation = null;
      notifyListeners();
      return;
    }

    _permissionGranted = await Geolocator.checkPermission();
    if (_permissionGranted == LocationPermission.denied) {
      _permissionGranted = await Geolocator.requestPermission();
      if (_permissionGranted != LocationPermission.whileInUse &&
          _permissionGranted != LocationPermission.always) {
        _currentLocation = null;
        notifyListeners();
        return;
      }
    }

    Geolocator.getPositionStream(
            locationSettings: const LocationSettings(
                accuracy: LocationAccuracy.best, distanceFilter: 0))
        .listen((Position position) {
      checkPermissionsAndService();
      if (!_serviceEnabled ||
          _permissionGranted != LocationPermission.whileInUse &&
              _permissionGranted != LocationPermission.always) {
        _currentLocation = null;
        notifyListeners();
        return;
      } else {
        _currentLocation = position;
        notifyListeners();
      }
    });
  }

  void checkPermissionsAndService() async {
    _serviceEnabled = await Geolocator.isLocationServiceEnabled();
    _permissionGranted = await Geolocator.checkPermission();

    if (!_serviceEnabled ||
        _permissionGranted != LocationPermission.whileInUse &&
            _permissionGranted != LocationPermission.always) {
      _currentLocation = null;
      notifyListeners();
    }
  }

  Future<void> updateLocation() async {
    _serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!_serviceEnabled) {
      _currentLocation = null;
      notifyListeners();
      return;
    }

    _permissionGranted = await Geolocator.checkPermission();
    if (_permissionGranted == LocationPermission.denied) {
      _permissionGranted = await Geolocator.requestPermission();
      if (_permissionGranted != LocationPermission.whileInUse &&
          _permissionGranted != LocationPermission.always) {
        _currentLocation = null;
        notifyListeners();
        return;
      }
    }

    if (_serviceEnabled &&
        (_permissionGranted == LocationPermission.whileInUse ||
            _permissionGranted == LocationPermission.always)) {
      _currentLocation = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.best);
      notifyListeners();
    }
  }

  Future<bool> isLocationServiceEnabledAndPermissionGranted() async {
    _serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!_serviceEnabled) {
      return false;
    }

    _permissionGranted = await Geolocator.checkPermission();
    if (_permissionGranted == LocationPermission.denied) {
      _permissionGranted = await Geolocator.requestPermission();
      if (_permissionGranted != LocationPermission.whileInUse &&
          _permissionGranted != LocationPermission.always) {
        return false;
      }
    }

    return _serviceEnabled &&
        (_permissionGranted == LocationPermission.whileInUse ||
            _permissionGranted == LocationPermission.always);
  }

  Position mapLocationDataToCurrentPosition(LocationData  locationData) {
    currentPosition = Position(
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        timestamp: locationData.time,
        accuracy: locationData.accuracy,
        altitude: locationData.altitude,
        heading: locationData.heading,
        speed: locationData.speed,
        speedAccuracy: locationData.speedAccuracy,
        isMocked: locationData.isMock,
        headingAccuracy: locationData.headingAccuracy,
        verticalAccuracy: locationData.verticalAccuracy, 
        altitudeAccuracy: locationData.altitudeAccuracy,
    );
    notifyListeners();
  }
}
