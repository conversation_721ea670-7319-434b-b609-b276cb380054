import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/pages/audit/audit_list_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/confirm_audit_model.dart';
import 'package:alink/pages/airport/audit/equipment_list_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/provider/locationProvider.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/app_expansion_tile.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:location/location.dart' as gps;
import 'package:provider/provider.dart';

import '../../../logger/logger.dart';

import '../../../bloc/audit_bloc/audit_bloc.dart';

class ConfirmAuditPage extends StatefulWidget {
  static const routeName = "confirm-audit";
  final ConfirmAuditModel confirmAuditModel;

  const ConfirmAuditPage({Key? key, required this.confirmAuditModel}) : super(key: key);

  @override
  _ConfirmAuditPageState createState() => _ConfirmAuditPageState();
}

class _ConfirmAuditPageState extends State<ConfirmAuditPage> {
  static const String className = '_ConfirmAuditPageState';

  late List<GlobalKey<AppExpansionTileState>> expansionKeyList;
  List<String> confirmLocationList = [];
  List<Equipment> globalEquipmentList = [];
  List<String> notFound = [];
  gps.LocationData? locationData;
  int unScannedEquip = 0, newEquip = 0;
  var equipmentRemoved = false;
  int removedCount = 0;
  var diffListIsFirstTime = true;
  var isFirstSaveDB = true;
  var isSaveDb = true;
  bool fetchingLocation = false;
  Logger logger = Logger();
  var inInContext = true;
  LocationProvider? locationProvider;
  bool serviceEnabled = false;
  LocationPermission? permission;

  @override
  void initState() {
    locationProvider = Provider.of<LocationProvider>(context, listen: false);
    expansionKeyList = [];
    unScannedEquip = 0;
    newEquip = 0;
    _initLocation();
    super.initState();
  }

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  RepairDetailApiCubit get repairDetailApiCubit => BlocProvider.of<RepairDetailApiCubit>(context);

  AuditListCubit get auditCubit => BlocProvider.of<AuditListCubit>(context);

  ServiceRequestBloc get serviceRequestBloc => BlocProvider.of<ServiceRequestBloc>(context);

  AuditBloc get auditBloc => BlocProvider.of<AuditBloc>(context);

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      Logger.i("Class Name: " + className);
    }
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                constraints: const BoxConstraints(maxWidth: 500),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ApplicationUtil.displayNotificationWidgetIfExist(context, EquipmentListPage.routeName),
                    _equipmentListAppBar(),
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics(),
                        ),
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 5),
                          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                          child: Column(
                            children: [
                              if (!(widget.confirmAuditModel.isEquipmentsValidated!))
                                Card(
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            children: [
                                              const FaIcon(
                                                FontAwesomeIcons.thumbsUp,
                                                size: 75,
                                                color: AppColor.greenSentColor,
                                              ),
                                              const SizedBox(
                                                height: 10,
                                              ),
                                              Text(
                                                AppLocalizations.of(context)!.wellDoneEquipmentScanned,
                                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                              ),
                                              const SizedBox(
                                                height: 10,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              else
                                BlocConsumer<ApiBloc, ApiState>(
                                  listener: (context, state) {
                                    if (state is ValidateError) {
                                      if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                                        Navigator.pushNamedAndRemoveUntil(context, LoginPage.routeName, (route) => false, arguments: true);
                                      }
                                    }
                                  },
                                  builder: (context, state) {
                                    var auditEquipmentListOriginal = [];
                                    if (state is ValidateError) {
                                      return Center(
                                        child: Text(state.errorMessage),
                                      );
                                    } else if (state is ValidatedAuditBarcodes) {
                                      if (inInContext == true && state.equipmentList.isNotEmpty) {
                                        List<Equipment> originalListOfEquipment = state.equipmentList;
                                        var listTagAuditEquipmentLocal = [];
                                        var listTagEquipmentApi = [];
                                        var listTagAuditEquipmentLocalDiff = [];

                                        var unScannedEquipments = [];
                                        var scannedEquipments = [];
                                        var newEquipments = [];

                                        var unScannedEquipmentsTag = [];
                                        var scannedEquipmentsTag = [];
                                        var newEquipmentsTag = [];

                                        ServiceRequestBloc.equipmentList.toSet();
                                        originalListOfEquipment.toSet();
                                        for (var localEquipment in ServiceRequestBloc.equipmentList) {
                                          listTagAuditEquipmentLocal.add(localEquipment.tag);
                                        }
                                        for (var apiEquipment in originalListOfEquipment) {
                                          listTagEquipmentApi.add(apiEquipment.tag);
                                        }

                                        for (var localEquipment in ServiceRequestBloc.equipmentList) {
                                          for (var apiEquipment in originalListOfEquipment) {
                                            if (apiEquipment.tag == localEquipment.tag && localEquipment.isScanned == false) {
                                              unScannedEquipments.add(apiEquipment);
                                              unScannedEquipmentsTag.add(apiEquipment.tag);
                                            }
                                            if (apiEquipment.tag == localEquipment.tag &&
                                                (localEquipment.status == "Scanned" || localEquipment.status == "Inactive") &&
                                                localEquipment.locationId == apiEquipment.locationId) {
                                              scannedEquipments.add(apiEquipment);
                                              scannedEquipmentsTag.add(apiEquipment.tag);
                                            }
                                            if (apiEquipment.tag == localEquipment.tag &&
                                                (AuditEquipmentCubit.auditLocation!['LOCATION_ID'] != apiEquipment.locationId)) {
                                              newEquipments.add(apiEquipment);
                                              newEquipmentsTag.add(apiEquipment.tag);
                                            }
                                          }
                                        }

                                        if (kDebugMode) {
                                          print("unScannedEquipments");
                                          print(unScannedEquipments);
                                          print(unScannedEquipmentsTag);

                                          print("scannedEquipments");
                                          print(scannedEquipments);
                                          print(scannedEquipmentsTag);

                                          print("newEquipments");
                                          print(newEquipments);
                                          print(newEquipmentsTag);
                                        }

                                        if (diffListIsFirstTime) {
                                          listTagAuditEquipmentLocalDiff =
                                              listTagAuditEquipmentLocal.toSet().difference(listTagEquipmentApi.toSet()).toList();
                                          diffListIsFirstTime = false;
                                        }
                                        if (listTagAuditEquipmentLocalDiff.isNotEmpty) {
                                          for (var diffEquip in listTagAuditEquipmentLocalDiff) {
                                            ServiceRequestBloc.equipmentList
                                                .removeWhere((element) => diffEquip == element.tag && element.status == "Unscanned");
                                            serviceRequestBloc.add(
                                                SaveEquipmentDataFromAuditStatus(ServiceRequestBloc.equipmentList, AuditEquipmentCubit.auditId!));
                                            for (var localEquip in ServiceRequestBloc.equipmentList) {
                                              if ((localEquip.tag == diffEquip) &&
                                                  (localEquip.status == "Scanned") &&
                                                  localEquip.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID']) {
                                                localEquip.status = "Movement";
                                                localEquip.locationId = null;
                                              }
                                            }
                                          }
                                        }

                                        for (var localEquip in ServiceRequestBloc.equipmentList) {
                                          for (var apiEquip in originalListOfEquipment) {
                                            if (localEquip.tag == apiEquip.tag) {
                                              if (localEquip.isInActiveEquipment == true && localEquip.isScanned == true) {
                                                apiEquip.status = "Scanned";
                                                apiEquip.isInActiveEquipment = false;
                                              } else if (localEquip.isInActiveEquipment == true && localEquip.isScanned == false) {
                                                apiEquip.status = "Inactive";
                                              }
                                            }
                                          }
                                        }
                                        var allTags = (listTagEquipmentApi + listTagAuditEquipmentLocal).toSet();
                                        if (listTagEquipmentApi.length > listTagAuditEquipmentLocal.length) {
                                          for (var element in listTagEquipmentApi) {
                                            if (!listTagAuditEquipmentLocal.contains(element)) {
                                              for (var apiEquipment in originalListOfEquipment) {
                                                if (apiEquipment.tag == element) {
                                                  AuditEquipment auditEquipment = AuditEquipment(
                                                      tag: apiEquipment.tag,
                                                      categoryName: apiEquipment.categoryName,
                                                      equipmentId: apiEquipment.equipmentId,
                                                      oldLocationId: apiEquipment.oldLocationId,
                                                      locationId: apiEquipment.locationId,
                                                      isInActiveEquipment: apiEquipment.isInActiveEquipment,
                                                      status: apiEquipment.status ?? "Unscanned",
                                                      name: apiEquipment.name);
                                                  int foundInList =
                                                      ServiceRequestBloc.equipmentList.indexWhere((element) => element.tag == auditEquipment.tag);
                                                  if (foundInList < 0) {
                                                    ServiceRequestBloc.equipmentList.add(auditEquipment);
                                                  } else {
                                                    var checkEquip = ServiceRequestBloc.equipmentList[foundInList];
                                                    if (checkEquip.tag != auditEquipment.tag && checkEquip.status != auditEquipment.status) {
                                                      ServiceRequestBloc.equipmentList.removeAt(foundInList);
                                                      ServiceRequestBloc.equipmentList.add(auditEquipment);
                                                    }
                                                  }
                                                  //ServiceRequestBloc.equipmentList.add(auditEquipment);
                                                  if (kDebugMode) {
                                                    print(ServiceRequestBloc.equipmentList.length.toString() +
                                                        "at line 281  while adding unscanned equipments for equipment list UI " +
                                                        className);
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                        for (var value in allTags) {
                                          if (listTagEquipmentApi.contains(value)) {
                                            for (var apiEquip in originalListOfEquipment) {
                                              if ((!(listTagAuditEquipmentLocal.contains(value))) && apiEquip.tag == value) {
                                                AuditEquipment auditEquipment = AuditEquipment(
                                                    tag: apiEquip.tag,
                                                    categoryName: apiEquip.categoryName,
                                                    equipmentId: apiEquip.equipmentId,
                                                    oldLocationId: apiEquip.oldLocationId,
                                                    locationId: apiEquip.locationId,
                                                    isInActiveEquipment: apiEquip.isInActiveEquipment,
                                                    status: apiEquip.status ?? "Unscanned",
                                                    name: apiEquip.name);
                                                int foundInList =
                                                    ServiceRequestBloc.equipmentList.indexWhere((element) => element.tag == auditEquipment.tag);
                                                if (foundInList < 0) {
                                                  ServiceRequestBloc.equipmentList.add(auditEquipment);
                                                } else {
                                                  var checkEquip = ServiceRequestBloc.equipmentList[foundInList];
                                                  if (checkEquip.tag != auditEquipment.tag && checkEquip.status != auditEquipment.status) {
                                                    ServiceRequestBloc.equipmentList.removeAt(foundInList);
                                                    ServiceRequestBloc.equipmentList.add(auditEquipment);
                                                  }
                                                }
                                                //ServiceRequestBloc.equipmentList.add(auditEquipment);
                                                if (kDebugMode) {
                                                  print(ServiceRequestBloc.equipmentList.length.toString() +
                                                      "at line 303 while adding unscanned equipments for equipment list UI " +
                                                      className);
                                                }
                                              }
                                            }
                                          } else if (listTagAuditEquipmentLocal.contains(value)) {
                                            for (var localEquip in ServiceRequestBloc.equipmentList) {
                                              if ((!(listTagEquipmentApi.contains(value))) && localEquip.tag == value) {
                                                if (localEquip.isScanned == false) {
                                                  auditEquipmentListOriginal.add(localEquip.tag);
                                                } else {
                                                  Equipment equipment = Equipment(
                                                    bomId: localEquip.bomId,
                                                    categoryName: localEquip.categoryName,
                                                    equipmentId: localEquip.equipmentId,
                                                    tag: localEquip.tag,
                                                    isInActiveEquipment: localEquip.isInActiveEquipment,
                                                    location: AuditEquipmentCubit.location,
                                                    locationId: localEquip.locationId,
                                                    name: localEquip.name,
                                                    status: localEquip.status,
                                                    oldLocationId: localEquip.oldLocationId,
                                                  );
                                                  originalListOfEquipment.add(equipment);
                                                }
                                              }
                                            }
                                            if (listTagAuditEquipmentLocalDiff.isNotEmpty) {
                                              for (var diffEquip in listTagAuditEquipmentLocalDiff) {
                                                ServiceRequestBloc.equipmentList
                                                    .removeWhere((element) => element.tag == diffEquip && element.status != "Scanned");
                                              }
                                            }
                                          }
                                        }
                                        if (auditEquipmentListOriginal.isNotEmpty) {
                                          ServiceRequestBloc.equipmentList.removeWhere((e) => auditEquipmentListOriginal.contains(e));
                                          if (removedCount == 1) {
                                            equipmentRemoved = true;
                                            removedCount += 1;
                                          } else {
                                            equipmentRemoved = false;
                                            removedCount += 1;
                                          }
                                        }
                                        if (kDebugMode) {
                                          print(
                                              "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++$equipmentRemoved+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
                                        }
                                        if (listTagEquipmentApi.isNotEmpty) {
                                          for (var value1 in listTagEquipmentApi) {
                                            for (var element in originalListOfEquipment) {
                                              if (value1 == element.tag && element.status == "UnScanned") {
                                                AuditEquipment auditEquipment = AuditEquipment(
                                                  bomId: element.bomId,
                                                  categoryName: element.categoryName,
                                                  equipmentId: element.equipmentId,
                                                  tag: element.tag,
                                                  isScanned: false,
                                                  isInActiveEquipment: element.isInActiveEquipment,
                                                  name: element.name,
                                                  status: "Unscanned",
                                                  locationId: element.locationId,
                                                  oldLocationId: element.oldLocationId,
                                                );
                                                ServiceRequestBloc.equipmentList.add(auditEquipment);
                                                if (kDebugMode) {
                                                  print(ServiceRequestBloc.equipmentList.length.toString() +
                                                      " at line 346 while adding unscanned equipments for equipment list UI " +
                                                      className);
                                                }
                                              }
                                            }
                                          }
                                        } else {
                                          for (var localEquipment in ServiceRequestBloc.equipmentList) {
                                            for (var apiEquipment in originalListOfEquipment) {
                                              if ((localEquipment.tag == apiEquipment.tag) && localEquipment.status == "Movement") {
                                                if (AuditEquipmentCubit.auditLocation!['LOCATION_ID'] == apiEquipment.locationId &&
                                                    apiEquipment.oldLocationId == null) {
                                                  localEquipment.locationId = apiEquipment.locationId;
                                                  localEquipment.oldLocationId = apiEquipment.oldLocationId;
                                                  localEquipment.name = apiEquipment.name;
                                                  localEquipment.isInActiveEquipment = apiEquipment.isInActiveEquipment;
                                                  localEquipment.equipmentId = apiEquipment.equipmentId;
                                                  localEquipment.categoryName = apiEquipment.categoryName;
                                                  localEquipment.bomId = apiEquipment.bomId;
                                                  localEquipment.isScanned = true;
                                                } else if (localEquipment.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID']) {
                                                  localEquipment.status = "Movement";
                                                  localEquipment.equipmentId = null;
                                                  localEquipment.name = apiEquipment.name;
                                                  apiEquipment.status = "Movement";
                                                } else {
                                                  localEquipment.status = "Scanned";
                                                  apiEquipment.status = "Scanned";
                                                }
                                              } else if ((localEquipment.tag == apiEquipment.tag) &&
                                                  localEquipment.status == "Scanned" &&
                                                  localEquipment.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID']) {
                                                if (localEquipment.oldLocationId != null &&
                                                    localEquipment.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID']) {
                                                  localEquipment.name = apiEquipment.name;
                                                  apiEquipment.status = "Movement";
                                                } else {
                                                  apiEquipment.status = localEquipment.status;
                                                }
                                              } else if ((localEquipment.tag == apiEquipment.tag) && localEquipment.status == "Unscanned") {
                                                apiEquipment.status = localEquipment.status;
                                              } else if ((localEquipment.tag == apiEquipment.tag) && localEquipment.isInActiveEquipment == true) {
                                                apiEquipment.status = "Inactive";
                                              }
                                            }
                                          }
                                        }
                                        for (var localEquipment in ServiceRequestBloc.equipmentList) {
                                          for (var apiEquipment in originalListOfEquipment) {
                                            if (localEquipment.tag == apiEquipment.tag && apiEquipment.status == null) {
                                              apiEquipment.status = localEquipment.status;
                                            }
                                          }
                                        }
                                        if (equipmentRemoved && listTagEquipmentApi != listTagAuditEquipmentLocal && removedCount == 1) {
                                          WidgetsBinding.instance.addPostFrameCallback((_) => _callEquipmentRemovedMessage(context));
                                        }
                                        originalListOfEquipment.toSet();
                                        ServiceRequestBloc.equipmentList.toSet();
                                        for (var localEquip in ServiceRequestBloc.equipmentList) {
                                          for (var apiEquip in originalListOfEquipment) {
                                            if (localEquip.tag == apiEquip.tag) {
                                              if (localEquip.isInActiveEquipment == null) {
                                                apiEquip.isInActiveEquipment = null;
                                              } else if (localEquip.isInActiveEquipment == true) {
                                                apiEquip.isInActiveEquipment = localEquip.isInActiveEquipment;
                                              }
                                              if (localEquip.oldLocationId != null) {
                                                apiEquip.oldLocationId = localEquip.oldLocationId;
                                              }
                                            }
                                          }
                                        }
                                        for (var apiEquipment in state.equipmentList) {
                                          for (var originalEquip in originalListOfEquipment) {
                                            for (var localEquipment in ServiceRequestBloc.equipmentList) {
                                              if (localEquipment.tag == apiEquipment.tag && apiEquipment.tag == originalEquip.tag) {
                                                originalEquip.locationId = apiEquipment.locationId;
                                                originalEquip.location = apiEquipment.location;
                                                originalEquip.status = apiEquipment.status;
                                                originalEquip.name = apiEquipment.name;
                                                localEquipment.name = apiEquipment.name;
                                                originalEquip.equipmentId = apiEquipment.equipmentId;
                                                originalEquip.bomId = apiEquipment.bomId;
                                                originalEquip.categoryId = apiEquipment.categoryId;
                                                originalEquip.oldLocationId = originalEquip.oldLocationId ?? apiEquipment.oldLocationId;
                                              }
                                            }
                                          }
                                        }

                                        for (var apiEquipment in originalListOfEquipment) {
                                          for (var localEquipment in ServiceRequestBloc.equipmentList) {
                                            if (localEquipment.tag == apiEquipment.tag &&
                                                apiEquipment.status == "Scanned" &&
                                                (localEquipment.oldLocationId != null || apiEquipment.oldLocationId != null) &&
                                                apiEquipment.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID']) {
                                              apiEquipment.status = "Movement";
                                              localEquipment.status = "Movement";
                                              localEquipment.name = apiEquipment.name;
                                              apiEquipment.oldLocationId = null;
                                              localEquipment.oldLocationId = null;
                                              localEquipment.locationId = apiEquipment.locationId;
                                            } else if (localEquipment.tag == apiEquipment.tag &&
                                                apiEquipment.status == "Scanned" &&
                                                apiEquipment.locationId == AuditEquipmentCubit.auditLocation!['LOCATION_ID'] &&
                                                (localEquipment.oldLocationId == null || apiEquipment.oldLocationId == null)) {
                                              apiEquipment.status = "Scanned";
                                              localEquipment.status = "Scanned";
                                            } else if (localEquipment.tag == apiEquipment.tag &&
                                                localEquipment.isScanned == false &&
                                                localEquipment.isInActiveEquipment == false) {
                                              apiEquipment.status = "Unscanned";
                                              localEquipment.status = "Unscanned";
                                            } else if (localEquipment.tag == apiEquipment.tag &&
                                                localEquipment.isScanned == false &&
                                                localEquipment.isInActiveEquipment == true) {
                                              apiEquipment.status = "Inactive";
                                              localEquipment.status = "Inactive";
                                            } else if (localEquipment.tag == apiEquipment.tag &&
                                                localEquipment.isScanned == true &&
                                                apiEquipment.locationId == AuditEquipmentCubit.auditLocation!['LOCATION_ID'] &&
                                                (localEquipment.oldLocationId == null || apiEquipment.oldLocationId == null)) {
                                              apiEquipment.status = "Scanned";
                                              localEquipment.status = "Scanned";
                                              localEquipment.name = apiEquipment.name;
                                              localEquipment.equipmentId = apiEquipment.equipmentId;
                                              localEquipment.locationId = apiEquipment.locationId;
                                              localEquipment.categoryName = apiEquipment.categoryName;
                                              localEquipment.bomId = apiEquipment.bomId;
                                            }
                                          }
                                        }
                                        var tempList = originalListOfEquipment;
                                        tempList.forEach((tempElement) {
                                          ServiceRequestBloc.equipmentList.forEach((srBlocElement) {
                                            if (tempElement.tag == srBlocElement.tag &&
                                                srBlocElement.status == "Scanned" &&
                                                srBlocElement.locationId == null) {}
                                          });
                                        });
                                        tempList.forEach((tempElement) {
                                          ServiceRequestBloc.equipmentList.forEach((srBlocElement) {
                                            if (tempElement.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID'] &&
                                                tempElement.status == "Scanned" &&
                                                tempElement.tag == srBlocElement.tag &&
                                                tempElement.oldLocationId == null) {
                                              tempElement.status = "Movement";
                                              //tempElement.oldLocationId = AuditEquipmentCubit.auditLocation!['LOCATION_ID'];
                                              srBlocElement.status = "Movement";
                                              tempElement.isInActiveEquipment = false;
                                              //srBlocElement.oldLocationId=AuditEquipmentCubit.auditLocation!['LOCATION_ID'];
                                            }
                                          });
                                        });
                                        tempList.removeWhere((element) =>
                                            (element.status == "Scanned" && element.locationId == AuditEquipmentCubit.auditLocation!['LOCATION_ID']));
                                        if (isFirstSaveDB) {
                                          var tempEquip;
                                          for (var element in ServiceRequestBloc.equipmentList) {
                                            if (element.isScanned == false) {
                                              tempEquip = element;
                                              continue;
                                            }
                                          }
                                          if (tempEquip == null) {
                                            serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!,
                                                ServiceRequestBloc.equipmentList.first.tag!, null, null, null, ServiceRequestBloc.equipmentList));
                                            serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatusByName(
                                                AuditEquipmentCubit.auditId!,
                                                ServiceRequestBloc.equipmentList.first.name!,
                                                ServiceRequestBloc.equipmentList.first,
                                                ServiceRequestBloc.equipmentList,
                                                null,
                                                null));
                                          } else {
                                            serviceRequestBloc.add(UpdateBarcodeDataInAudit(
                                                AuditEquipmentCubit.auditId!, tempEquip.tag!, null, null, null, ServiceRequestBloc.equipmentList));
                                            serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatusByName(AuditEquipmentCubit.auditId!,
                                                tempEquip.name!, tempEquip, ServiceRequestBloc.equipmentList, null, null));
                                          }

                                          isFirstSaveDB = false;
                                        }
                                        tempList.removeWhere((element) => element.equipmentId == null && element.name == null);
                                        return _equipmentListUI(tempList, false);
                                      } else if (inInContext == true && state.equipmentList.isEmpty) {
                                        //ServiceRequestBloc.equipmentList.clear();
                                        return _equipmentListUI([], true);
                                      }
                                    } else if (state is ValidatingAuditBarcodes) {
                                      return const Center(
                                        child: CircularProgressIndicator(),
                                      );
                                    }
                                    return const Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  },
                                )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              widget.confirmAuditModel!.isConditionAudit!
                  ? Container()
                  : Positioned(
                      left: 20,
                      bottom: 20,
                      child: Container(
                        child: _getSaveButton(ServiceRequestBloc.equipmentList, onTapSaveButton, AppLocalizations.of(context)!.save),
                      ),
                    ),
              Container(
                  padding: widget.confirmAuditModel!.isConditionAudit!
                      ? const EdgeInsets.symmetric(horizontal: 120, vertical: 20)
                      : const EdgeInsets.only(top: 20, bottom: 20, right: 80, left: 170),
                  child: _getSubmitButton(globalEquipmentList))
            ],
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.of(context).pop(true);
        },
      ),
    );
  }

  _callEquipmentRemovedMessage(BuildContext context) {
    return ApplicationUtil.showWarningAlertDialog(context,
        title: AppLocalizations.of(context)!.alert,
        negativeLabel: AppLocalizations.of(context)!.okay,
        desc: AppLocalizations.of(context)!.scannedItemsRemoved);
  }

  _equipmentListAppBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(left: 10, top: 5),
          child: Text(
            AppLocalizations.of(context)!.confirm + " " + widget.confirmAuditModel.serviceType!,
            style: const TextStyle(color: AppColor.blackTextColor, fontSize: AppConstant.toolbarTitleFontSize, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  _equipmentListUI(List<Equipment> equipmentList, bool noEquipments) {
    bool isNewItemTitleSet = false;
    bool isUnScannedItemTitleSet = false;
    List<Widget> equipmentWidgetList = [];
    List<String?> notFoundIndexes = [];
    var equipmentNotFoundCount = 0;
    var unScannedEquips = 0;
    var thumbsUpString = "";

    if (widget.confirmAuditModel.isConditionAudit! && widget.confirmAuditModel.scannedUnscannedCount! > 0) {
      thumbsUpString = AppLocalizations.of(context)!.wellDoneEquipmentScanned;
    } else if (equipmentList.isEmpty && noEquipments && widget.confirmAuditModel.scannedUnscannedCount! > 0) {
      thumbsUpString = AppLocalizations.of(context)!.wellDoneEquipmentScanned;
    } else if (equipmentList.isEmpty && noEquipments) {
      thumbsUpString = widget.confirmAuditModel.serviceType! == "Task"
          ? AppLocalizations.of(context)!.noEquipmentsStillCompleteTask
          : AppLocalizations.of(context)!.noEquipmentsStillCompleteAudit;
    }

    for (var equipment in equipmentList) {
      if (equipment.status == "Unscanned") {
        unScannedEquips += 1;
      }
    }
    if (kDebugMode) {
      for (var equipment in equipmentList) {
        print("++++++++++++++++++++++++++++++++++state.equipment++++++++++++++++++++++++++++++++++");
        print(equipment.toString() + "\n");
      }
      for (var equipment in ServiceRequestBloc.equipmentList) {
        print("++++++++++++++++++++++++++++++++++ServiceRequestBloc.equipment++++++++++++++++++++++++++++++++++");
        print(equipment.toString() + "\n");
      }
    }
    for (var element in equipmentList) {
      if (element.status == "NOT FOUND") {
        notFoundIndexes.add(element.tag);
      }
    }
    notFoundIndexes.join(",");
    equipmentList.removeWhere((element) => element.status == "NOT FOUND");
    if (equipmentList.isNotEmpty) {
      for (int i = 0; i < equipmentList.length; i++) {
        unScannedEquip = 0;
        newEquip = 0;
        Equipment equipment = equipmentList[i];
        expansionKeyList.add(GlobalKey());
        if (equipment.status == "Movement") {
          newEquip += 1;
          equipmentWidgetList.add(isNewItemTitleSet == false ? _getNewEquip(isNewItemTitleSet) : const SizedBox());
          isNewItemTitleSet = true;
          equipmentWidgetList.add(getSingleListTileWidget(equipment, i));
        } else if (equipment.status != "Scanned") {
          unScannedEquip += 1;
          if (!widget.confirmAuditModel.isConditionAudit!) {
            equipmentWidgetList.add(isUnScannedItemTitleSet == false ? _getUnScannedItems(isUnScannedItemTitleSet) : const SizedBox());
            isUnScannedItemTitleSet = true;
            equipmentWidgetList.add(getSingleListTileWidget(equipment, i));
          }
        }
        globalEquipmentList = equipmentList;
        if (listIsLast(i, equipmentList)) {
          equipmentWidgetList.add(
            const SizedBox(
              height: 60,
            ),
          );
        }
        if (kDebugMode) {
          print("unscanned equipment count" + unScannedEquip.toString());
          print("new equipment count" + newEquip.toString());
          print("total equipment count" + ServiceRequestBloc.equipmentList.length.toString());
        }
      }
    }
    if (equipmentList.isEmpty &&
        (((equipmentList.isEmpty && !(widget.confirmAuditModel.isConditionAudit!)) || (unScannedEquips == 0 && newEquip != 0)) ||
            ((widget.confirmAuditModel.isConditionAudit!) && widget.confirmAuditModel.scannedUnscannedCount! > 0))) {
      inInContext
          ? equipmentWidgetList.add(Card(
              child: Container(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          const FaIcon(
                            FontAwesomeIcons.thumbsUp,
                            size: 75,
                            color: AppColor.greenSentColor,
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Text(
                            thumbsUpString,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ))
          : const CircularProgressIndicator();
    }
    if (equipmentList.isEmpty) {
      for (var element in ServiceRequestBloc.equipmentList) {
        if (element.status == "Movement") {
          equipmentNotFoundCount += 1;
        }
      }
    }
    if (equipmentNotFoundCount > 1) {
      for (var element in ServiceRequestBloc.equipmentList) {
        if (element.status == "Movement") {
          equipmentWidgetList.add(_getNoItemFoundAlert(element));
        }
      }
    }
    return Column(
      children: equipmentWidgetList,
    );
  }

  getSingleListTileWidget(Equipment equipment, int index) {
    var container = Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(border: Border.all(color: AppColor.greyBorderColor), borderRadius: BorderRadius.circular(10)),
      child: AppExpansionTile(
          initiallyExpanded: false,
          onExpansionChanged: (value) {
            if (value) {
              for (var element in expansionKeyList) {
                if (element.currentState != null) {
                  if (expansionKeyList[index].currentState != element.currentState) {
                    element.currentState!.collapse();
                  }
                }
              }
            }
          },
          key: expansionKeyList[index],
          leading: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                _getLeadingIcon(equipment, index),
                equipment.name != null
                    ? const SizedBox(
                        width: 5,
                      )
                    : const SizedBox(),
                _getTag(equipment, index),
              ]),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: _getEquipmentName(equipment, index),
              ),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.025),
          children: <Widget>[
            const Divider(
              thickness: 1,
            ),
            _checkAndReturnEquipmentDetailExpansionDetailUI(equipment, index),
          ]),
    );
    return container;
  }

  bool listIsLast(int index, List<Equipment> equipmentList) => index == equipmentList.length - 1;

  _getSubmitButton(List<Equipment> equipmentList) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      height: 50,
      width: double.infinity,
      child: ElevatedButton(
        /* style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
        ),*/
        onPressed: onPressedSubmitButton,
        child: Container(
          padding:fetchingLocation ? const EdgeInsets.symmetric(vertical: 8) : const EdgeInsets.symmetric(vertical: 14),
          child: fetchingLocation
              ? const Center(
                  child: CircularProgressIndicator(color: Colors.white,),
                )
              : Text(
                  AppLocalizations.of(context)!.submit,
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
        ),
      ),
    );
  }

  onPressedSubmitButton() async{
    Logger.i("Started onPressedSubmitButton in confirm_audit_page");
    var equipmentList = globalEquipmentList;
    int searchedIndex = equipmentList.indexWhere((element) =>
        element.status == "Movement" && element.oldLocationId == null);

    ///When It is not able to get the location in the init()
    if (locationData == null) {
      setState(() {
        fetchingLocation = true;
      });

      ///Fetching the location details using GeoLocator
      var locationDetail = await ApplicationUtil.getGeoLocatorLocation();
      setState(() {
        fetchingLocation = false;
      });
      //Taking too long to fetch the location
      if (locationDetail.runtimeType == String && locationDetail == "TIMEOUT") {
        ApplicationUtil.showSnackBar(
            context: context,
            message: "Fetching Location is taking time please try again");
        return;
      } else if(locationDetail.runtimeType == String && locationDetail == "WEB"){
        Logger.i("Web platform detected - skipping location validation continue submit");
      }else {
        if (locationDetail != null) {
          Logger.i(
              "Lat: ${locationDetail!.latitude}, Long: ${locationDetail.longitude}, Accuracy: ${locationDetail.accuracy}");
          locationData = locationDetail;
          onPressedSubmitButton();
          return;
        } else {
          Logger.i("Unable to get the location in confirm_audit_page");
          ApplicationUtil.showWarningAlertDialog(
            context,
            title: AppLocalizations.of(context)!.locationPermissionRequired,
            desc: kIsWeb
                ? AppLocalizations.of(context)!.appRequiresLocationPermissionforweb
                : AppLocalizations.of(context)!.appRequiresLocationPermission,
            negativeLabel: AppLocalizations.of(context)!.okay,
          );
          return;
        }
      }
    }
    if (searchedIndex >= 0) {
      ApplicationUtil.showSnackBar(
          context: context,
          message:
              '${AppLocalizations.of(context)!.confirm} ${equipmentList[searchedIndex].categoryName} ${equipmentList[searchedIndex].name}  location.');
    } else {
      for (var equipmentApi in equipmentList) {
        for (var equipmentDb in ServiceRequestBloc.equipmentList) {
          if (equipmentApi.tag == equipmentDb.tag) {
            equipmentDb.equipmentId = equipmentApi.equipmentId;
            equipmentDb.name = equipmentApi.name;
            if (equipmentApi.status != null) {
              equipmentDb.status = equipmentApi.status!;
            }
          }
        }
      }
      if (widget.confirmAuditModel.isConditionAudit!) {
        if (widget.confirmAuditModel.scannedUnscannedCount! == 0) {
          ApplicationUtil.showSnackBar(
              context: context,
              message: AppLocalizations.of(context)!.conditionAuditMessage);
        } else {
          submitAuditToServer(ServiceRequestBloc.equipmentList,
              widget.confirmAuditModel.isConditionAudit!, false);
        }
      } else {
        submitAuditToServer(ServiceRequestBloc.equipmentList,
            widget.confirmAuditModel.isConditionAudit!, false);
      }
    }
  }

  _getLeadingIcon(Equipment equipment, int index) {
    int index = ServiceRequestBloc.equipmentList.indexWhere((element) => element.tag == equipment.tag);
    if (index >= 0) {
      AuditEquipment auditEquipment = ServiceRequestBloc.equipmentList[index];
      if (auditEquipment.isScanned && auditEquipment.status == "Scanned") {
        return const FaIcon(
          FontAwesomeIcons.checkCircle,
          color: Color(0xff259B24),
        );
      } else if (auditEquipment.isScanned && auditEquipment.status == "Movement") {
        return const FaIcon(
          FontAwesomeIcons.solidMagic,
          color: Colors.pink,
        );
      } else {
        return const FaIcon(FontAwesomeIcons.barcode);
      }
    }
    //return Container();
  }

  _getTag(Equipment equipment, int index) {
    int index = ServiceRequestBloc.equipmentList.indexWhere((element) => element.tag == equipment.tag);
    if (index >= 0) {
      AuditEquipment auditEquipment = ServiceRequestBloc.equipmentList[index];
      if (auditEquipment.isScanned && auditEquipment.status == "Scanned") {
        return Text(
          equipment.tag!,
          style: const TextStyle(height: 1.2, fontSize: 18, fontWeight: FontWeight.bold, color: AppColor.blackTextColor),
        );
      } else if (auditEquipment.isScanned && auditEquipment.status == "Movement") {
        return const SizedBox();
      } else {
        return Text(
          equipment.tag!,
          style: const TextStyle(height: 1.2, fontSize: 18, fontWeight: FontWeight.bold, color: AppColor.blackTextColor),
        );
      }
    }
  }

  _getEquipmentName(Equipment equipment, int index) {
    if (kDebugMode) {
      print(equipment.name);
      print(equipment.tag);
      print(equipment.oldLocationId);
      print("<---equipment.name--->");
    }
    return Text(
      equipment.name!,
      style: const TextStyle(
        fontSize: 20,
        color: Color(0xff101010),
      ),
    );
  }

  _checkAndReturnEquipmentDetailExpansionDetailUI(Equipment equipment, int index) {
    if (equipment.locationId == AuditEquipmentCubit.auditLocation!['LOCATION_ID'] && equipment.oldLocationId == null) {
      return setEquipmentAsInActiveUI(equipment, index);
    } else {
      equipment.status = "Movement";
      return _confirmMovementUi(
          auditLocation: AuditEquipmentCubit.location, equipment: equipment, equipmentLocation: equipment.location!, index: index);
    }
  }

  setEquipmentAsInActiveUI(Equipment equipment, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Text('Mark ${equipment.name} as inactive',
                    textAlign: TextAlign.center, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 17)),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
              equipment.isInActiveEquipment != null && equipment.isInActiveEquipment == true
                  ? AppLocalizations.of(context)!.thisEquipmentIsMarkedAsInactive
                  : AppLocalizations.of(context)!.thisEquipmentWillBeInActive,
              textAlign: TextAlign.center,
              style: const TextStyle(fontWeight: FontWeight.bold)),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              equipment.isInActiveEquipment == null || equipment.isInActiveEquipment == false
                  ? ElevatedButton(
                      style: ButtonStyle(
                        padding: MaterialStateProperty.all(
                          const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                        ),
                        backgroundColor: MaterialStateProperty.all(AppColor.greenSentColor),
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                        ),
                      ),
                      onPressed: () {
                        var auditEquipment;
                        for (var element in ServiceRequestBloc.equipmentList) {
                          if (element.tag == equipment.tag) {
                            if (kDebugMode) {
                              print("element");
                              print(element);
                            }
                            element.isInActiveEquipment = true;
                            element.status = "Inactive";
                            auditEquipment = element;
                          }
                        }
                        equipment.isInActiveEquipment = true;
                        equipment.status = "Inactive";
                        apiBloc.add(RefreshValidatedAuditData());
                        expansionKeyList[index].currentState!.collapse();
                        if (kDebugMode) {
                          print(equipment);
                        }
                        serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!, equipment.tag!, equipment.isInActiveEquipment,
                            null, null, ServiceRequestBloc.equipmentList));
                        serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatusByName(AuditEquipmentCubit.auditId!, equipment.name!, auditEquipment,
                            ServiceRequestBloc.equipmentList, equipment.isInActiveEquipment, null));
                      },
                      child: Text(
                        AppLocalizations.of(context)!.confirm,
                        style: const TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    )
                  : Container(),
              equipment.isInActiveEquipment == true
                  ? ElevatedButton(
                      style: ButtonStyle(
                        padding: MaterialStateProperty.all(
                          const EdgeInsets.symmetric(vertical: 8, horizontal: 14),
                        ),
                        backgroundColor: MaterialStateProperty.all(AppColor.redColor),
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                        ),
                      ),
                      onPressed: () {
                        var auditEquipment;
                        for (var element in ServiceRequestBloc.equipmentList) {
                          if (element.tag == equipment.tag) {
                            if (kDebugMode) {
                              print("element");
                              print(element);
                            }
                            element.isInActiveEquipment = false;
                            element.status = "Inactive";
                            auditEquipment = element;
                          }
                        }
                        equipment.isInActiveEquipment = false;
                        equipment.status = "Inactive";
                        apiBloc.add(RefreshValidatedAuditData());
                        expansionKeyList[index].currentState!.collapse();
                        serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!, equipment.tag!, equipment.isInActiveEquipment,
                            null, null, ServiceRequestBloc.equipmentList));
                        serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatusByName(AuditEquipmentCubit.auditId!, equipment.name!, auditEquipment,
                            ServiceRequestBloc.equipmentList, equipment.isInActiveEquipment, null));
                      },
                      child: Text(
                        AppLocalizations.of(context)!.reject,
                        style: const TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    )
                  : Container()
            ],
          )
        ],
      ),
    );
  }

  _confirmMovementUi(
      {List<Map<String, dynamic>>? auditLocation,
      required Equipment equipment,
      required int index,
      required List<Map<String, dynamic>> equipmentLocation}) {
    List equipmentLocationStringList = [];
    List auditLocationStringList = [];
    String equipmentLocationString = '';
    String auditLocationString = '';
    for (var element in equipmentLocation) {
      equipmentLocationStringList.add(element["NAME"]);
    }
    for (var element in auditLocation!) {
      auditLocationStringList.add(element["NAME"]);
    }
    num getStringListSize(List arr1, List arr2) {
      if (arr1.length < arr2.length) {
        return arr1.length;
      } else {
        return arr2.length;
      }
    }

    for (int i = 0; i < getStringListSize(equipmentLocationStringList, auditLocationStringList); i++) {
      if (equipmentLocationStringList[i] == auditLocationStringList[i]) {
        // equipmentLocationStringList.removeAt(i);
        // auditLocationStringList.removeAt(i);
      }
      auditLocationString = auditLocationStringList.join(', ');
      equipmentLocationString = equipmentLocationStringList.join(', ');
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  equipmentLocationString,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 17),
                ),
              ),
              const SizedBox(
                width: 5,
              ),
              const FaIcon(FontAwesomeIcons.arrowRight),
              const SizedBox(
                width: 5,
              ),
              Expanded(
                child: Text(auditLocationString, textAlign: TextAlign.center, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 17)),
              ),
            ],
          ),
          Text(
              equipment.oldLocationId != null ? AppLocalizations.of(context)!.equipmentMoved : AppLocalizations.of(context)!.equipmentMovementNoticed,
              textAlign: TextAlign.center,
              style: const TextStyle(fontWeight: FontWeight.bold)),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              equipment.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID'] && equipment.oldLocationId == null
                  ? ElevatedButton(
                      style: ButtonStyle(
                        padding: MaterialStateProperty.all(
                          const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                        ),
                        backgroundColor: MaterialStateProperty.all(AppColor.greenSentColor),
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                        ),
                      ),
                      onPressed: () {
                        var auditEquipment;
                        for (var element in ServiceRequestBloc.equipmentList) {
                          if (element.tag == equipment.tag) {
                            element.oldLocationId = equipment.locationId;
                            element.locationId = auditLocation.last['LOCATION_ID'];
                            auditEquipment = element;
                          }
                        }
                        equipment.oldLocationId = equipment.locationId;
                        equipment.locationId = auditLocation.last['LOCATION_ID'];
                        expansionKeyList[index].currentState!.collapse();
                        serviceRequestBloc.add(UpdateBarcodeDataInAudit(AuditEquipmentCubit.auditId!, equipment.tag!, null, equipment.oldLocationId,
                            auditLocation.last['LOCATION_ID'], ServiceRequestBloc.equipmentList));
                        serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatusByName(AuditEquipmentCubit.auditId!, equipment.name!, auditEquipment,
                            ServiceRequestBloc.equipmentList, null, equipment.oldLocationId));
                        apiBloc.add(RefreshValidatedAuditData());
                      },
                      child: Text(
                        AppLocalizations.of(context)!.confirm,
                        style: const TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    )
                  : Container(),
              const SizedBox(
                width: 15,
              ),
              equipment.locationId != AuditEquipmentCubit.auditLocation!['LOCATION_ID'] && equipment.oldLocationId == null
                  ? ElevatedButton(
                      style: ButtonStyle(
                        padding: MaterialStateProperty.all(
                          const EdgeInsets.symmetric(vertical: 8, horizontal: 14),
                        ),
                        backgroundColor: MaterialStateProperty.all(AppColor.redColor),
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15.0),
                          ),
                        ),
                      ),
                      onPressed: () {
                        ServiceRequestBloc.equipmentList.removeWhere((element) => element.tag == equipment.tag);
                        apiBloc.add(ValidateAuditBarcodes(equipmentList: ServiceRequestBloc.equipmentList, auditId: AuditEquipmentCubit.auditId!));
                        serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatus(AuditEquipmentCubit.auditId!, equipment.tag!, true));
                        expansionKeyList[index].currentState!.collapse();
                      },
                      child: Text(
                        AppLocalizations.of(context)!.reject,
                        style: const TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    )
                  : Container()
            ],
          )
        ],
      ),
    );
  }

  submitAuditToServer(List<AuditEquipment>? equipmentList, bool isConditionAudit, bool? isSaveAudit) {
    //auditCubit.submitAudit(equipmentList, AuditEquipmentCubit.auditId!, locationData,isConditionAudit);
    auditBloc.add(SubmitAuditList(
        auditId: AuditEquipmentCubit.auditId!,
        isConditionAudit: isConditionAudit,
        equipmentList: equipmentList,
        locationData: locationData,
        isSaveAudit: isSaveAudit));
    const CircularProgressIndicator();
    var nav = Navigator.of(context);
    if (widget.confirmAuditModel.isFromMap!) {
      nav.pop('refresh');
      nav.pop('refresh');
      nav.pop('refresh');
    } else {
      nav.pop('refresh');
      nav.pop('refresh');
    }
  }

  void _initLocation() async {
    var locationDetails = await ApplicationUtil.getGeoLocatorLocation();
    if (locationDetails != null && locationDetails.runtimeType != String) {
      locationData = locationDetails;
    } else if (locationDetails == "WEB") {
      Logger.i("Web platform detected in init - skipping location initialization");
    } else if(locationDetails == "TIMEOUT"){
      Logger.i("Timeout detected in init - skipping location initialization");
    }
  }

  _getNewEquip(bool isNewItemTitleSet) {
    Widget newText = const Row(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(
              'New Equipment',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    );
    return newText;
  }

  _getUnScannedItems(bool isUnScannedItemTitleSet) {
    Widget newText = const Row(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(
              'Unscanned Equipment',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
        )
      ],
    );
    return newText;
  }

  Widget _getNoItemFoundAlert(AuditEquipment equipment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        border: Border.all(color: AppColor.greyBorderColor),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 3),
      child: Row(
        children: [
          const FaIcon(
            Icons.crisis_alert_outlined,
            color: AppColor.orangeColor,
          ),
          const SizedBox(
            width: 5,
          ),
          Expanded(
            child: Text(
              equipment.tag.toString() + " " + AppLocalizations.of(context)!.auditItemNotFound,
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ],
      ),
    );
  }

  _getSaveButton(List<AuditEquipment> equipmentList, VoidCallback onTap, String label) {
    return SizedBox(
      width: 150,
      child: Padding(
        padding: const EdgeInsets.only(right: 10.0),
        child: ElevatedButton(
            onPressed: onTap,
            /*style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
            ),*/
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 14),
              child: Text(
                AppLocalizations.of(context)!.save,
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
            )),
      ),
    );
  }

  onTapSaveButton() {
    submitAuditToServer(ServiceRequestBloc.equipmentList, widget.confirmAuditModel.isConditionAudit!, true);
  }
}